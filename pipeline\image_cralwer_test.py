import os
import json
import urllib.request
import random
import time
import cv2
import yt_dlp
import psycopg2
import logging
import uuid
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from fake_useragent import UserAgent
import subprocess



# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
start_time = time.time()

# 샘플 JSON 데이터 (Chrome 드라이버 문제로 인해 YouTube만 테스트)
json_data = [
    {"source": "youtube",  "keyword": "겨울", "target_count": 10},
    # {"source": "google", "keyword": "남해", "target_count": 10},
    # {"source": "pixabay", "keyword": "바다", "target_count": 10},
    # {"source": "unsplash", "keyword": "한국", "target_count": 10}
]

# =====================================================================================
# 0. 프론트엔드에서 제공받는 json파일 형태 target_data 에 저장
# ====================================================================================
def generate_job_id():
    """고유한 job_id 생성"""
    return str(uuid.uuid4())

def create_target_data(json_data, job_id):
    default_values = {
        "job_id": job_id,
        "end_date": None,
        "processing_count": "0/{target_count}",
        "collected_count": 0,
        "stats": "수집 중",
        "start_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }

    complete_data_list = []
    for item in json_data:
        complete_data = {**default_values, **item}
        if "target_count" in complete_data:
            complete_data["processing_count"] = f"0/{complete_data['target_count']}"
        complete_data_list.append(complete_data)
    return complete_data_list

def insert_target_data(data, cur, conn):
    try:
        for record in data:
            # job_id 컬럼이 없는 경우를 대비해 job_id 제외하고 삽입
            cur.execute("""
                INSERT INTO target_data (
                    source, keyword, target_count,
                    start_date, end_date,
                    processing_count, collected_count, stats
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                record["source"],
                record["keyword"],
                record["target_count"],
                record["start_date"],
                record["end_date"],
                f"0/{record['target_count']}",
                0,
                "수집 중"
            ))
        conn.commit()
        logging.info("✅ target_data 삽입 완료")
    except Exception as e:
        logging.error(f"❌ target_data 저장 오류: {e}")
        conn.rollback()

def update_progress(cur, conn, job_id, source, keyword, collected_count, target_count, status="수집 중"):
    """진행률 업데이트 함수"""
    try:
        processing_count = f"{collected_count}/{target_count}"

        # 목표 달성 시 상태 변경
        if collected_count >= target_count:
            status = "완료"
            end_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            # job_id 컬럼이 없는 경우를 대비해 source와 keyword로만 업데이트
            cur.execute("""
                UPDATE target_data
                SET processing_count = %s, collected_count = %s, stats = %s, end_date = %s
                WHERE source = %s AND keyword = %s
            """, (processing_count, collected_count, status, end_date, source, keyword))
        else:
            cur.execute("""
                UPDATE target_data
                SET processing_count = %s, collected_count = %s, stats = %s
                WHERE source = %s AND keyword = %s
            """, (processing_count, collected_count, status, source, keyword))

        conn.commit()
        logging.info(f"📊 진행률 업데이트: {source} {keyword} - {processing_count} ({status})")
    except Exception as e:
        logging.error(f"❌ 진행률 업데이트 오류: {e}")
        conn.rollback()

# =====================================================================================
# 1. 공통 설정 및 DB 연결
# =====================================================================================
def connect_to_postgresql(dbname, user, password, host, port):
    try:
        conn = psycopg2.connect(
            dbname=dbname, user=user, password=password,
            host=host, port=port
        )
        return conn, conn.cursor()
    except Exception as e:
        logging.error(f"DB 연결 실패: {e}")
        return None, None

def create_directory(keyword, source):
    today = datetime.now().strftime('%y%m%d')
    base = os.path.join("/Volumes/pj1/datalake", keyword, source)
    os.makedirs(base, exist_ok=True)
    
    count = 1
    sub = os.path.join(base, f"{today}_{count}.mp4") if source == "youtube" else os.path.join(base, today)
    while os.path.exists(sub):
        count += 1
        sub = os.path.join(base, f"{today}_{count}.mp4") if source == "youtube" else os.path.join(base, f"{today}_{count}")
    os.makedirs(sub)

    return (sub, os.path.join(sub, f"{today}_{count}"), f"{today}_{count}") if source == "youtube" else sub

def get_chrome_version():
    result = subprocess.run(
        ["/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "--version"],
        capture_output=True, text=True
    )
    version = result.stdout.strip().replace("Google Chrome ", "")
    return version


# def get_chrome_driver():
#     options = Options()
#     options.add_argument('--headless')
#     options.add_argument('--no-sandbox')
#     options.add_argument('--disable-dev-shm-usage')
#     options.add_argument('--disable-gpu')
#     options.add_argument('--disable-infobars')
#     options.add_argument('--disable-extensions')
#     options.add_argument('--disable-popup-blocking')
#     options.add_argument('--incognito')
#     ua = UserAgent()
#     options.add_argument(f'user-agent={ua.random}')
#     return webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)

def get_chrome_driver():
    options = Options()
    options.add_argument('--headless')  # 브라우저 GUI 없이 실행
    options.add_argument('--no-sandbox')  # 샌드박스 사용 안함 (Linux 환경에서 주로 사용)
    options.add_argument('--disable-dev-shm-usage')  # 메모리 부족 방지 옵션
    options.add_argument('--disable-gpu')  # GPU 가속 비활성화
    options.add_argument('--disable-infobars')  # "Chrome이 자동화에 의해 제어되고 있음" 표시 제거
    options.add_argument('--disable-extensions')  # 확장 프로그램 비활성화
    options.add_argument('--disable-popup-blocking')  # 팝업 차단 비활성화
    options.add_argument('--incognito')  # 시크릿 모드

    ua = UserAgent()
    options.add_argument(f'user-agent={ua.random}')  # 무작위 User-Agent 설정

    # # binary_location 설정 (macOS의 경우)
    # options.binary_location = '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'

    # 특정 크롬드라이버 버전 명시적으로 지정
    # driver_version으로 옵션을 설정해줘야한다.
    # chrome_driver_path = ChromeDriverManager(driver_version="138.0.7204.50").install()
    
    # return webdriver.Chrome(service=Service(chrome_driver_path), options=options)
    
    # WebDriver Manager로 ChromeDriver 설치 및 서비스 생성
    # service = Service(ChromeDriverManager().install())

    # # WebDriver 초기화
    # driver = webdriver.Chrome(service=service)

    # chrome_driver_path = ChromeDriverManager(driver_version="137.0.7151.70").install()
    # chrome_driver_path = ChromesDriverManager().install()
    
    #성공    
    return webdriver.Chrome(service = webdriver.ChromeService(), options=options)

def human_typing(elem, text):
    for ch in text:
        elem.send_keys(ch)
        time.sleep(random.uniform(0.1, 0.3))

def scroll_down(driver):
    driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
    time.sleep(2)


# unsplash 용
def scroll_and_load(driver):
    for _ in range(2):
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(2)
        try:
            btn = WebDriverWait(driver, 2).until(
                EC.presence_of_element_located((By.XPATH, "//button[contains(text(),'더 로드')]"))
            )
            driver.execute_script("arguments[0].scrollIntoView({block:'center'});", btn)
            driver.execute_script("arguments[0].click();", btn)
            logging.info("✅ 더 로드 버튼 클릭 완료")
            time.sleep(2)
            break
        except:
            continue

# =====================================================================================
# 2.  이미지 수집 처리
# =====================================================================================
# 2-1. 구글 이미지 수집
def collect_google_images_url(driver, keyword, count):
    
    image_urls = set()
    while len(image_urls) < count:
        images = driver.find_elements(By.CSS_SELECTOR, 'img')
        for img in images:
            src = img.get_attribute('src') or img.get_attribute('data-src') or img.get_attribute('data-iurl')
            if src and src.startswith(('http://', 'https://')):
                image_urls.add(src)
            if len(image_urls) >= count:
                break
        scroll_down(driver)

    # except Exception as e:
    #     logging.warning(f"이미지 수집 중 예외 발생: {type(e).__name__}: {e}")  ## 예외 타입과 메시지를 함께 출력

    # logging.info(f"🖼️ 최종 수집된 이미지 URL 개수: {len(image_urls)}")
    return list(image_urls)[:count]

# 2-1. 구글 이미지 저장
def save_google_images(urls, save_dir, keyword, cur, conn, job_id, target_count):
    total_saved=0
    for idx, url in enumerate(urls, 1):
        try:
            path = os.path.join(save_dir, f"{keyword}_{idx}.jpg")
            req = urllib.request.Request(url, headers={'User-Agent': 'Mozilla/5.0'})

            # 이미지 수집 시간 측정 시작
            google_start_time=time.time()

            with urllib.request.urlopen(req) as res, open(path, 'wb') as f:
                f.write(res.read())

            ### 이미지 수집 시간 측정 종료
            google_end_time = time.time()
            google_elapsed_time = google_end_time - google_start_time  # ### 수집 시간 계산
            logging.info(f"❶ 이미지 저장 : {path} (수집 시간: {google_elapsed_time:.2f}초)")  # ### 수집 시간 로그에 추가

            cur.execute("""
                INSERT INTO image_metadata(id, keyword, registered_date, file_path, source, file_name, video_url, job_id)
                VALUES (%s, %s, %s, %s, %s, %s, NULL, %s)
                ON CONFLICT (id) DO NOTHING
            """, (url, keyword, datetime.now(), path, "google", os.path.basename(path), job_id))
            conn.commit()
            total_saved+=1

            # 진행률 업데이트 (매 이미지마다)
            update_progress(cur, conn, job_id, "google", keyword, total_saved, target_count)

        except Exception as e:
            logging.warning(f"❌ 이미지 저장 실패: {url}, {e}")
    logging.info("===================================================================================")
    logging.info(f"✅ Google 이미지 저장 완료 : {keyword}, 총 {total_saved}장")
    logging.info("===================================================================================")
# 2-1. 구글 main 함수
def process_google(keyword, count, conn, cur, job_id):
     # DB 연결 - 독립적인 스레드 활용을 위해 각각 독립적인 DB 연결
    conn, cur = connect_to_postgresql("localtest", "admin", "signgate1!", "211.232.75.159", "45432")
    driver = get_chrome_driver()
    base_url = "https://images.google.com/?hl=ko"
    driver.get(base_url)

    try:
        save_dir = create_directory(keyword, "google")
        # ▶ 막 생성한 디렉터리 경로를 target_data에 기록
        cur.execute("""
            UPDATE target_data
            SET file_path = %s
            WHERE source = %s AND keyword = %s
        """, (save_dir, "google", keyword))
        conn.commit()

        search_box = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.NAME, "q")))
        search_box.clear()
        human_typing(search_box, keyword)
        search_box.send_keys(Keys.RETURN)
        time.sleep(2)

        image_urls = collect_google_images_url(driver, keyword, count)
        save_google_images(image_urls, save_dir, keyword, cur, conn, job_id, count)
        # logging.info(f"✅ Google 이미지 수집 완료: {keyword}")
    except Exception as e:
        logging.error(f"❌ Google 수집 실패 ({keyword}): {e}")
        # 실패 시에도 상태 업데이트
        update_progress(cur, conn, job_id, "google", keyword, 0, count, "실패")
    finally:
        cur.close()
        conn.close()
        driver.quit()

def load_processed_videos(cur):
    try:
        cur.execute("SELECT video_url FROM video_data")
        rows = cur.fetchall()
        return {row[0] for row in rows}
    except Exception as e:
        logging.warning(f"⚠️ video_data 테이블 조회 실패: {e}")
        return set()  # 빈 set 반환

# 2-2. 유튜브 URL 수집
def collect_youtube_video_url(json_data, processed_videos, cur, conn):
    url_result = {}
    for item in json_data:
        if item.get("source").lower() != "youtube":
            continue

        keyword = item["keyword"]
        target_total = item["target_count"]
        source = item["source"]
        # logging.info(f"\n🔍 {source} 사이트에서 '{keyword}' 검색 시작 (목표 총 길이: {target_total}초)")

        ydl_opts = {
                    'quiet': True,
                    'extract_flat': True,
                    'skip_download': True,
                    'no_check_certificate': True
                    }
        url_list = []
        total_duration = 0
        # 검색 배치 크기 설정
        search_batch = 50

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            while total_duration <= target_total:
                query = f"ytsearch{search_batch}:{keyword}"
                result = ydl.extract_info(query, download=False)

                for entry in result.get('entries', []):
                    duration = entry.get('duration')
                    url = entry.get('url')
                    if duration is None or duration > 40:
                        continue
                    if url in url_list or url in processed_videos:
                        logging.info(f"🔁 중복 URL 스킵: {url}")
                        continue
                    url_list.append(url)
                    total_duration += duration
                    if total_duration > target_total:
                        break

                if total_duration > target_total or search_batch > 5000:
                    break
                search_batch += 50

        for url in url_list:
            try:
                cur.execute("""
                    INSERT INTO video_data(video_url, keyword, source, registered_at)
                    VALUES (%s, %s, %s, (NOW() AT TIME ZONE 'Asia/Seoul'))
                    ON CONFLICT (video_url) DO NOTHING
                """, (url, keyword, "youtube"))
            except Exception as e:
                logging.warning(f"⚠️ video_data 테이블 삽입 실패: {e}")
                # 테이블이 없어도 계속 진행
                pass
        try:
            conn.commit()
        except Exception as e:
            logging.warning(f"⚠️ 커밋 실패: {e}")
        logging.info(f"✅ '{keyword}' 수집 완료: {len(url_list)}개 영상 다운로드 완료")
        url_result[keyword] = url_list

    return url_result
# 2-2. 다운받을 유튜브 비디오 URL 수집
def download_youtube_video_url(url_list, save_dir):
    ydl_opts = {
        'quiet': False,
        'format': 'mp4',
        'outtmpl': os.path.join(save_dir, '%(title).50s.%(ext)s'),
        'no_check_certificate': True
    }
    downloaded = []
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        for url in url_list:
            # 시간 측정
            video_start_time = time.time()
            try:
                info = ydl.extract_info(url, download=True)
                path = ydl.prepare_filename(info)
                downloaded.append(path)

                 # 다운로드 시간 측정 종료
                video_end_time = time.time()
                video_elapsed_time = video_end_time - video_start_time  # 소요 시간 계산
                logging.info(f"✅ 유튜브 영상 다운로드 완료: {url} (수집 시간: {video_elapsed_time:.2f}초)")  # 로그 추가
            except Exception as e:
                logging.error(f"다운로드 실패 {url}: {e}")
    return downloaded

# 2-2. 유튜브 비디오 프레임 저장
def capture_frames_per_second(video_path, save_dir, keyword, start_count, cur, conn, video_url, job_id, target_count):
    today = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # save_dir(프레임 폴더)가 없으면 만들어 두기
    os.makedirs(save_dir, exist_ok=True)


    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        logging.error(f"파일 열기 실패: {video_path}")
        return start_count

    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total = int(cap.get(cv2.CAP_PROP_FRAME_COUNT)) // fps
    count = start_count

    for sec in range(total):
        cap.set(cv2.CAP_PROP_POS_FRAMES, sec * fps)
        ret, frame = cap.read()
        if not ret:
            continue

        file_path = os.path.join(save_dir, f"{keyword}_{count}.jpg")
        cv2.imwrite(file_path, frame)
        logging.info(f"❷ 이미지 저장 : {file_path}")

        metadata = {
            "id": f"{video_url}#{count}",
            "video_url": video_url,
            "keyword": keyword,
            "registered_date": today,
            "file_path": file_path,
            "source": "youtube",
            "file_name": os.path.basename(file_path),
            "job_id": job_id
        }
        insert_q = """
        INSERT INTO image_metadata
          (id, video_url, keyword, registered_date, file_path, source, file_name, job_id)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        ON CONFLICT (id) DO NOTHING;
        """
        cur.execute(insert_q, tuple(metadata.values()))
        conn.commit()
        count += 1

        # 진행률 업데이트 (매 프레임마다)
        current_saved = count - start_count
        update_progress(cur, conn, job_id, "youtube", keyword, current_saved, target_count)

    cap.release()
    return count

# 2-2. 유튜브 메인 함수
def process_youtube(keyword, count, conn, cur, job_id):
     # DB 연결 - 독립적인 스레드 활용을 위해 각각 독립적인 DB 연결

    conn, cur = connect_to_postgresql("localtest", "admin", "signgate1!", "211.232.75.159", "45432")
    total_saved = 0  # 변수 초기화를 try 블록 밖으로 이동
    try:
        processed_videos = load_processed_videos(cur)
        video_url_dict = collect_youtube_video_url(json_data, processed_videos, cur, conn)

        urls = video_url_dict.get(keyword, [])
        if not urls:
            logging.error(f"⚠️ '{keyword}' 수집된 영상 없음")
            update_progress(cur, conn, job_id, "youtube", keyword, 0, count, "실패")
            return

        video_dir, frame_dir, _ = create_directory(keyword, "youtube")

          # ▶ 프레임 저장 폴더 경로를 target_data에 기록 (job_id 제거)
        cur.execute("""
            UPDATE target_data
            SET file_path = %s
            WHERE source = %s AND keyword = %s
        """, (frame_dir, "youtube", keyword))
        conn.commit()

        files = download_youtube_video_url(urls, video_dir)

        cnt = 1
        for video_file, video_url in zip(files, urls):
            cnt_before = cnt
            cnt = capture_frames_per_second(
                video_file, frame_dir, keyword, cnt, cur, conn, video_url, job_id, count
            )
            total_saved += (cnt - cnt_before)
    except Exception as e:
        logging.error(f"❌ YouTube 수집 실패 ({keyword}): {e}")
        update_progress(cur, conn, job_id, "youtube", keyword, 0, count, "실패")
    finally:
        cur.close()
        conn.close()

    if total_saved > count:
        logging.info("===================================================================================")
        logging.info(f"🎯 목표 초과: ' youtube {keyword}' 키워드에서 {total_saved - count}장 더 저장됨 (총 {total_saved}장)")
        logging.info("===================================================================================")
    else:
        logging.info("===================================================================================")
        logging.info(f"✅ YouTube 프레임 저장 완료: {keyword}, 총 {total_saved}장")
        logging.info("===================================================================================")
        
        
# 2-3. pixabay 
# 이미지 URL 수집 함수
# def collect_pixabay_images_url(driver, keyword, count,image_urls,cur):

# 2-3. pixabay 
# 이미지 URL 수집 함수
# def collect_pixabay_images_url(driver, keyword, count,image_urls,cur):

# 2-3. pixabay 이미지 URL 수집       
def collect_pixabay_images_url(driver, keyword, count, image_urls, cur, conn):
    page_number = 1
    base_url = "https://pixabay.com/images/search"

    try:
        while len(image_urls) < count:
            # 현재 페이지의 이미지 요소 모두 찾기
            images = driver.find_elements(By.CSS_SELECTOR, 'img')

            for img in images:
                try:
                    src = (
                        img.get_attribute('src') or
                        img.get_attribute('data-src') or
                        img.get_attribute('data-lazy')
                    )
                    # src가 None이거나 비어있거나, 유효한 URL이 아니면 건너뛰기
                    if not src or not src.startswith(('http://', 'https://')) or not src.endswith(('jpg', 'jpeg', 'png')):
                        continue

                    # DB에 동일 URL 존재 여부 확인
                    select_query = "SELECT 1 FROM image_metadata WHERE id = %s LIMIT 1;"
                    try:
                        cur.execute(select_query, (src,))
                        result = cur.fetchone()
                    except Exception as db_err:
                        logging.warning(f"❗ DB 중복 체크 실패: {type(db_err).__name__}: {db_err}")
                        conn.rollback()
                        continue

                    if not result and src not in image_urls:
                        image_urls.add(src)
                        # logging.info(f"🖼️ 수집된 이미지: {src}")

                    if len(image_urls) >= count:
                        break
                except Exception as e_img:
                    logging.warning(f"❗ 이미지 URL 처리 중 오류: {e_img}")
                    continue

            # 더 수집이 필요한 경우 다음 페이지로 이동
            if len(image_urls) < count:
                # 한번만 실행
                if page_number == 1:
                    driver.find_element(By.CLASS_NAME, 'button--9NFL').send_keys(keyword + Keys.RETURN)
                    
                page_number += 1
                next_url = f"{base_url}/{keyword}/?pagi={page_number}"
                logging.info(f" 다음 페이지 이동: {next_url}")
                driver.get(next_url)
                time.sleep(2)

    except Exception as e:
        logging.error(f"❌ pixabay 수집 실패 ({keyword}): {type(e).__name__}: {e}")
        conn.rollback()
    logging.info("===================================================================================")
    logging.info(f"✅ pixabay 이미지 저장 완료 : {keyword}, 총 {len(image_urls)}장 수집")
    logging.info("===================================================================================")
    return image_urls
# 2-3. pixabay 이미지 저장
def save_pixabay_images(image_urls, save_dir, keyword, source, cur, conn, job_id, target_count):
    total_saved = 0
    for idx, url in enumerate(image_urls):
        try:
            ## 중복된 이미지 URL 확인
            select_query = "SELECT 1 FROM image_metadata WHERE id = %s LIMIT 1;"
            cur.execute(select_query, (url,))
            result = cur.fetchone()

            if result:  # 중복된 URL이면 건너뛰기
                print(f"중복된 이미지 URL : {url}")
                continue

            file_path = os.path.join(save_dir, f"{keyword}_{idx+1}.jpg")
            headers = {'User-Agent': 'Mozilla/5.0'}

            # 이미지 수집 시간 측정 시작
            pixabay_start_time=time.time()

            req = urllib.request.Request(url, headers=headers)
            with urllib.request.urlopen(req) as response, open(file_path, 'wb') as out_file:
                out_file.write(response.read())

            ### 이미지 수집 시간 측정 종료
            pixabay_end_time = time.time()
            pixabay_elapsed_time = pixabay_end_time - pixabay_start_time  # ### 수집 시간 계산
            logging.info(f"❸ 이미지 저장 : {file_path} (수집 시간: {pixabay_elapsed_time:.2f}초)")  # ### 수집 시간 로그에 추가

            # PostgreSQL에 데이터 삽입
            insert_query = """
            INSERT INTO image_metadata (id, keyword, registered_date, file_path, source, file_name, job_id)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (id) DO NOTHING;
            """
            cur.execute(insert_query, (
                url,
                keyword,
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                file_path,
                source,
                os.path.basename(file_path),
                job_id
            ))
            conn.commit()
            total_saved += 1

            # 진행률 업데이트 (매 이미지마다)
            update_progress(cur, conn, job_id, source, keyword, total_saved, target_count)

        except Exception as e:
            conn.rollback()
            print(f"이미지 저장 실패 : {url}, Error: {e}")

# 2-3. pixabay 메인 함수
def process_pixabay(keyword, count, conn, cur, job_id):
    # DB 연결 - 독립적인 스레드 활용을 위해 각각 독립적인 DB 연결
    conn, cur = connect_to_postgresql("localtest", "admin", "signgate1!", "211.232.75.159", "45432")
    driver=get_chrome_driver()
    base_url= "https://pixabay.com/ko/"
    driver.get(base_url)

    try:
        save_dir= create_directory(keyword, "pixabay")

        cur.execute("""
            UPDATE target_data
            SET file_path = %s
            WHERE source = %s AND keyword = %s
        """, (save_dir, "pixabay", keyword))
        conn.commit()

        input_box = WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.NAME, 'search')))
        human_typing(input_box, keyword)
        input_box.send_keys(Keys.ENTER)
        time.sleep(2)


        # image_urls 초기화
        image_urls = set()
        image_urls = collect_pixabay_images_url(driver, keyword, count, image_urls,cur,conn)
        print(f"이미지 수집 : ",{image_urls})
        if not image_urls:
            logging.warning(f"⚠️ Pixabay에서 '{keyword}'에 대한 이미지를 찾을 수 없습니다.")
            update_progress(cur, conn, job_id, "pixabay", keyword, 0, count, "실패")
            return
        # keyword_image_urls[keyword]
        save_pixabay_images(image_urls, save_dir, keyword,"pixabay",cur,conn, job_id, count)
        # logging.info(f"✅ pixabay 이미지 수집 완료: {keyword}")
    except Exception as e:
        logging.error(f"❌ pixabay 수집 실패 ({keyword}): {e}")
        update_progress(cur, conn, job_id, "pixabay", keyword, 0, count, "실패")
    finally:
        cur.close()
        conn.close()
        driver.quit()
    

    end_time = time.time()
    total_time_taken = end_time - start_time

    # logging.info(f"총 수집 개수: {total_images_collected}")
    # logging.info(f"총 걸린 시간: {total_time_taken:.2f} 초")


#2-4. unsplash 이미지 url 수집
# 이미지 URL 수집
def collect_unsplash_images_url(driver, keyword, count, cur, conn):
    images_collected = 0
    image_urls = set()
    while images_collected < count:
        # scroll_and_load(driver)
        images = driver.find_elements(By.CLASS_NAME, 'czQTa')

        # 한 번만 호출될지 여부 추적하는 플래그
        scroll_called = False

        for img in images:

            # img 안에 src 또는 srcset 속성이 있는 경우에만 로직 실행
            if 'srcset' in img.get_attribute('outerHTML'):

                srcset = img.get_attribute('srcset')
                src = srcset.strip().split(',')[-1].strip().split(' ')[0]
                # if not src or not src.startswith(('http://', 'https://')) or not src.endswith(('jpg', 'jpeg', 'png')):
                #     continue

                try:
                    select_query = "SELECT 1 FROM image_metadata WHERE id = %s LIMIT 1;"
                    cur.execute(select_query, (src,))
                    result = cur.fetchone()
                    if result:  # 이미 DB에 존재하는 경우 건너뜀
                        continue
                except Exception as db_err:
                    logging.warning(f"❗ DB 중복 체크 실패: {type(db_err).__name__}: {db_err}")
                    conn.rollback()
                    continue

                if src not in image_urls:
                    image_urls.add(src)
                    images_collected += 1
                    # logging.info(f"🖼️ 수집된 이미지: {src}")

                # image_urls=[item for item in image_urls if item !='']

                # 매번 scroll_down을 호출하지 않고, 이미지 수집 url이 더 이상 없고, images_collected가 count보다 작을 때만 호출
                # if not scroll_and_load(driver) and images_collected < count:
            # 플래그가 False인 경우에만 호출
            if not scroll_called:
                scroll_and_load(driver)    # 한 번만 실행
                scroll_called = True               # 다시 호출되지 않도록 설정
            # 해당 페이지에서 이미지 url 수집이 완료되고, images_collected가 count보다 작을 때만 스크롤 다운
            if len(image_urls) >= count:
                break
        scroll_down(driver)




    return image_urls

# 2-4. unsplash 이미지 저장
def save_unsplash_images(image_urls, save_dir, keyword, cur, conn, job_id, target_count):
    total_saved = 0
    for idx, url in enumerate(image_urls):
        try:
            file_path = os.path.join(save_dir, f"{keyword}_{idx+1}.jpg")
            headers = {'User-Agent': 'Mozilla/5.0'}

            # 이미지 수집 시간 측정 시작
            unsplash_start_time=time.time()

            req = urllib.request.Request(url, headers=headers)
            with urllib.request.urlopen(req) as response, open(file_path, 'wb') as out_file:
                out_file.write(response.read())

            ### 이미지 수집 시간 측정 종료
            unsplash_end_time = time.time()
            unsplash_elapsed_time = unsplash_end_time - unsplash_start_time  # ### 수집 시간 계산
            logging.info(f"❹ 이미지 저장 : {file_path} (수집 시간: {unsplash_elapsed_time:.2f}초)")  # ### 수집 시간 로그에 추가


            cur.execute("""
                INSERT INTO image_metadata
                  (id, keyword, registered_date, file_path, source, file_name, video_url, job_id)
                VALUES (%s, %s, %s, %s, %s, %s, NULL, %s)
                ON CONFLICT (id) DO NOTHING;
            """, (
                url,
                keyword,
                datetime.now(),
                file_path,
                "unsplash",
                os.path.basename(file_path),
                job_id
            ))
            conn.commit()
            total_saved += 1

            # 진행률 업데이트 (매 이미지마다)
            update_progress(cur, conn, job_id, "unsplash", keyword, total_saved, target_count)

        except Exception as e:
            logging.warning(f"❌ 저장 실패: {url}, 에러: {e}")
    # logging.info(f"✅ [{keyword}] 저장 완료: 총 {total_saved}장")
    logging.info("===================================================================================")
    logging.info(f"✅ unsplash 이미지 저장 완료 : {keyword}, 총 {total_saved}장")
    logging.info("===================================================================================")

# 2-4. unsplash 메인 함수
def process_unsplash(keyword, count, conn, cur, job_id):
# DB 연결 - 독립적인 스레드 활용을 위해 각각 독립적인 DB 연결
    conn, cur = connect_to_postgresql("localtest", "admin", "signgate1!", "211.232.75.159", "45432")
    driver=get_chrome_driver()
    base_url= "https://unsplash.com/ko"
    driver.get(base_url)

    try:
        save_dir= create_directory(keyword, "unsplash")

        cur.execute("""
            UPDATE target_data
            SET file_path = %s
            WHERE source = %s AND keyword = %s
        """, (save_dir, "unsplash", keyword))
        conn.commit()

        input_box = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, '.DevoO.kt4Uc'))
            )
        input_box.click()
        time.sleep(0.5)
        for _ in range(10):
            input_box.send_keys(Keys.BACKSPACE)
            time.sleep(0.1)
        human_typing(input_box, keyword)
        input_box.send_keys(Keys.RETURN)
        time.sleep(2)

        save_dir = create_directory(keyword, "unsplash")
        image_urls = collect_unsplash_images_url(driver, keyword, count, cur, conn)
        save_unsplash_images(image_urls, save_dir, keyword, cur, conn, job_id, count)

        if not image_urls:
            logging.warning(f"⚠️ unsplash에서 '{keyword}'에 대한 이미지를 찾을 수 없습니다.")
            update_progress(cur, conn, job_id, "unsplash", keyword, 0, count, "실패")
            return

    except Exception as e:
        logging.error(f"❌ unsplash 수집 실패 ({keyword}): {e}")
        update_progress(cur, conn, job_id, "unsplash", keyword, 0, count, "실패")
    finally:
        cur.close()
        conn.close()
        driver.quit()
    

    end_time = time.time()
    total_time_taken = end_time - start_time






# =====================================================================================
# 4. 병렬 실행 Main
# =====================================================================================
if __name__ == '__main__':
    # 고유한 job_id 생성
    job_id = generate_job_id()
    logging.info(f"🆔 생성된 Job ID: {job_id}")

    # JSON 데이터를 target_data에 저장
    conn, cur = connect_to_postgresql("localtest", "admin", "signgate1!", "211.232.75.159", "45432")
    insert_target_data(create_target_data(json_data, job_id), cur, conn)

    # 병렬 처리
    with ThreadPoolExecutor(max_workers=4) as executor:

        futures = []
        for entry in json_data:
            src = entry["source"]
            if src == "pixabay":
                futures.append(executor.submit(process_pixabay, entry["keyword"], entry["target_count"], conn, cur, job_id))
            elif src == "google":
                futures.append(executor.submit(process_google, entry["keyword"], entry["target_count"], conn, cur, job_id))
            elif src == "youtube":
                futures.append(executor.submit(process_youtube, entry["keyword"], entry["target_count"], conn, cur, job_id))
            elif src == "unsplash":
                futures.append(executor.submit(process_unsplash, entry["keyword"], entry["target_count"], conn, cur, job_id))



        for f in futures:
            f.result()

    cur.close()
    conn.close()

    total_time = time.time() - start_time
    logging.info("===================================================================================")
    logging.info(f"✅ 전체 작업 완료 - 총 소요 시간: {total_time:.2f}초")
    logging.info(f"🆔 완료된 Job ID: {job_id}")
    logging.info("===================================================================================")