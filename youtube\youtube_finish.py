import os
import json
import yt_dlp
from datetime import datetime
import psycopg2  
import cv2

# PostgreSQL 연결 함수
def connect_to_postgresql(dbname, user, password, host, port):
    try:
        conn = psycopg2.connect(
            dbname=dbname,
            user=user,
            password=password,
            host=host,
            port=port
        )
        cur = conn.cursor()
        return conn, cur
    except psycopg2.Error as e:
        print(f"데이터베이스 연결 실패: {e}")
        return None, None

# 유튜브 URL만 수집하는 함수
def collect_youtube_url_list_from_json(json_path, max_video_duration=20):
    
    # 다운받을 url_result 변수 초기화
    url_result = {}
    
    # 프론트엔드에서 제공하는 JSON 파일 읽기
    with open(json_path, 'r', encoding='utf-8') as f:
        keywords = json.load(f)
    
    # 해당되는 source(수집사이트)만 키워드 반복 수집
    for item in keywords:
        if item.get("source").lower() != "youtube":
            continue

        # 키워드와 목표 총 길이 설정
        keyword = item.get("keyword")
        target_total_duration = item.get("target_count")
        print(f"\n🔍 '{keyword}' 키워드 검색 시작 (목표 총 길이: {target_total_duration}초)")

        # yt-dlp 옵션 설정
        ydl_opts = {
            'quiet': True, # 출력 최소화
            'extract_flat': True, # 메타데이터만 추출
            'skip_download': True # 실제 다운로드는 하지 않음
        }

        url_list = []
        total_duration = 0
        search_batch = 50

        # yt-dlp를 사용하여 유튜브 검색 및 메타데이터 수집
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            while total_duration <= target_total_duration:
                search_query = f"ytsearch{search_batch}:{keyword}"
                search_result = ydl.extract_info(search_query, download=False)

                for entry in search_result.get('entries', []):
                    title = entry.get('title')
                    duration = entry.get('duration')
                    url = entry.get('url')

                    if not (title and duration is not None and duration <= max_video_duration):
                        continue

                    if url in url_list:
                        continue

                    # # ✅ 중복 여부 PostgreSQL에서 확인 
                    # cur.execute("SELECT 1 FROM image_metadata WHERE id = %s", (url,))
                    # if cur.fetchone():
                    #     print(f"🔁 이미 수집된 URL: {url}")
                    #     continue

                    url_list.append(url)
                    total_duration += duration
                    # 동영상 길이가 max_video_duration 이하인 경우에만 수집
                    if total_duration > target_total_duration:
                        print(f"⚠️ '{keyword}' 초과됨: 총합 {total_duration}초 (초과 {total_duration - target_total_duration}초)")
                        break

                if total_duration > target_total_duration:
                    break

                search_batch += 50
                if search_batch > 500:
                    print(f"⚠️ '{keyword}' 충분한 영상 확보 실패")
                    break

        url_result[keyword] = url_list

    return url_result

# NAS 폴더 생성 함수
def create_directory(keyword, source):
    today = datetime.now().strftime('%y%m%d')
    # NAS 마운트 포인트 설정
    nas_mount_point = '/Volumes/pj1'
    base_dir = os.path.join(nas_mount_point, 'datalake', keyword, source)

    os.makedirs(base_dir, exist_ok=True)

    count = 1
    video_folder_name = f"{today}_{count}.mp4"
    full_video_dir = os.path.join(base_dir, video_folder_name)
    
    # 중복 체크
    while os.path.exists(full_video_dir):
        count += 1
        video_folder_name = f"{today}_{count}.mp4"
        full_video_dir = os.path.join(base_dir, video_folder_name)
    
    os.makedirs(full_video_dir)

    # 하위 저장 디렉토리: 250504_1
    sub_dir = os.path.join(full_video_dir, f"{today}_{count}")
    os.makedirs(sub_dir)

    return full_video_dir, sub_dir, f"{today}_{count}"  # 각각: mp4 저장 경로, 이미지 저장 경로, prefix
    # full_video_dir: 동영상 저장 경로 => '/Volumes/pj1/datalake/봄/youtube/250507_2.mp4'
    # sub_dir : 이미지 저장 경로 => '/Volumes/pj1/datalake/봄/youtube/250507_2/250507_2'
    # prefix : '250507_2' => 동영상과 이미지 저장 시 사용




# ⬇️ 유튜브 다운로드 함수
def download_youtube_videos(url_list, save_dir):
    source="youtube"
    ydl_opts = {
        'quiet': False,
        'format': 'mp4',
        'outtmpl': os.path.join(save_dir, '%(title).50s.%(ext)s')
    }



    downloaded_files= []
    # 
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        for url in url_list:
            try:
                print(f"⬇️ 다운로드 시작: {url}")
                video_info = ydl.extract_info(url, download=True)
                file_path = ydl.prepare_filename(video_info)
                downloaded_files.append(file_path)
                print(f"###완료###:{file_path}")

            except Exception as e:
                print(f"다운로드 또는 저장 실패: {url} | 에러: {e}")
    return downloaded_files




# ▶영상에서 1초에 1장씩 프레임 캡처 함수
import cv2
import os

def capture_frames_per_second(video_path, save_dir,keyword,start_count,cur,conn,video_url):
    
    ## 오늘 날짜 문자열 생성 ##
    today = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 동영상 파일 열기
    cap = cv2.VideoCapture(video_path)
    print(f'동영상 경로 >>> {video_path}')
    if not cap.isOpened():
        raise ValueError(f"Cannot open video file: {video_path}")
        return start_count
    
    # FPS와 총 프레임 수 가져오기
    fps = int(cap.get(cv2.CAP_PROP_FPS))  # cv2.CAP_PROP_FPS : 5
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT)) # CAP_PROP_FRAME_COUNT : 7 total_frame=241
    duration = total_frames // fps  # 동영상 길이(초 단위) duration=8
     
    count = start_count  # 시작 번호 받음

    # 1초마다 프레임 캡처
    for second in range(duration):
        frame_number = second * fps
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
        ret, frame = cap.read()
        
        if not ret:
            print(f"{second}초 프레임 읽기 실패")
            continue
        
        # 이미지 저장 경로 설정
        file_path = os.path.join(save_dir, f"{keyword}_{count}.jpg")
        cv2.imwrite(file_path, frame)
        print(f"저장완료 : {file_path}")
       
        metadata = {
            "id": f"{video_url}#{count}",  # URL + 번호 조합
            "keyword": keyword,
            "registered_date": today,
            "file_path": file_path,
            "source": "youtube",
            "file_name": os.path.basename(file_path),
        }
        insert_query = """
        INSERT INTO image_metadata (id, keyword, registered_date, file_path, source, file_name)
        VALUES (%s, %s, %s, %s, %s, %s)
        ON CONFLICT (id) DO NOTHING;
        """
        try:
            cur.execute(insert_query, (
                metadata["id"],
                metadata["keyword"],
                metadata["registered_date"],
                metadata["file_path"],
                metadata["source"],
                metadata["file_name"],
            ))
            conn.commit()
        except Exception as e:
            print(f"❌ 메타데이터 저장 실패: {e}")



        count+=1

    # 리소스 해제
    cap.release()
    return count  # 마지막 번호 반환


# Main 실행 함수
if __name__ == "__main__":
    source = "youtube"

    # DB 연결
    conn, cur = connect_to_postgresql("data", "admin", "admin", "211.222.333.44", "5432")

    
    if not conn or not cur:
        print("데이터베이스 연결에 실패했습니다. 프로그램을 종료합니다."y)
        exit(1)

    video_url_dict = collect_youtube_url_list_from_json("keywords.json")

    # 프론트엔드에서 제공하는 JSON 파일 읽기
    for keyword, url_list in video_url_dict.items():
        if not url_list:
            print(f"⚠️ '{keyword}'는 수집된 URL이 없습니다. 건너뜁니다.")
            continue

        print(f"\n '{keyword}' 저장 폴더 생성 중...")
        # save_dir = create_directory(keyword, "youtube")
        video_dir, frame_dir, prefix = create_directory(keyword, "youtube")
        
        

        print(f" '{keyword}' 다운로드 시작...")
        video_files=download_youtube_videos(url_list,video_dir) # video_files=downloaded_files
        
        current_count = 1  # 프레임 번호 시작

        # 동영상 파일에서 프레임 추출
        for video_file, video_url in zip(video_files, url_list):              # url_list와 순서대로 매핑
            print(f" '{video_file}'에서 프레임 추출 중... (원본 URL: {video_url})")
            current_count = capture_frames_per_second(video_file, frame_dir, keyword, current_count, cur, conn, video_url)  # video_url 전달
             

    # 최종 커넥션 정리
    cur.close()
    conn.close()        